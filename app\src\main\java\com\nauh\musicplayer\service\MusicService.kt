package com.nauh.musicplayer.service

import android.app.PendingIntent
import android.content.Intent
import android.os.Bundle
import android.util.Log
import androidx.media3.common.AudioAttributes
import androidx.media3.common.C
import androidx.media3.common.MediaItem
import androidx.media3.common.MediaMetadata
import androidx.media3.common.Player
import androidx.media3.common.PlaybackException
import androidx.media3.datasource.DefaultHttpDataSource
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.exoplayer.source.DefaultMediaSourceFactory
import androidx.media3.session.MediaSession
import androidx.media3.session.MediaSessionService
import com.google.common.util.concurrent.Futures
import com.google.common.util.concurrent.ListenableFuture
import com.nauh.musicplayer.data.model.Song
import com.nauh.musicplayer.ui.MainActivity

/**
 * Background music service using ExoPlayer and MediaSession
 * Handles audio playback, notifications, and media controls
 */
class MusicService : MediaSessionService() {

    private var mediaSession: MediaSession? = null
    private lateinit var player: ExoPlayer



    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "MusicService onCreate")
        try {
            initializePlayer()
            initializeMediaSession()
            Log.d(TAG, "MusicService initialized successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize MusicService", e)
            stopSelf()
        }
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "MusicService onStartCommand")

        // Ensure player is initialized
        if (!this::player.isInitialized || mediaSession == null) {
            Log.w(TAG, "Player or MediaSession is null, reinitializing...")
            try {
                if (!this::player.isInitialized) {
                    initializePlayer()
                }
                if (mediaSession == null) {
                    initializeMediaSession()
                }
            } catch (e: Exception) {
                Log.e(TAG, "Failed to reinitialize player", e)
                stopSelf()
                return START_NOT_STICKY
            }
        }

        return START_STICKY // Restart service if killed
    }
    
    private fun initializePlayer() {
        Log.d(TAG, "Initializing ExoPlayer")

        // Create a custom HTTP data source factory with better error handling
        val httpDataSourceFactory = DefaultHttpDataSource.Factory()
            .setUserAgent("MusicPlayer/1.0")
            .setConnectTimeoutMs(30000)
            .setReadTimeoutMs(30000)
            .setAllowCrossProtocolRedirects(true)

        val mediaSourceFactory = DefaultMediaSourceFactory(this)
            .setDataSourceFactory(httpDataSourceFactory)

        player = ExoPlayer.Builder(this)
            .setMediaSourceFactory(mediaSourceFactory)
            .setAudioAttributes(
                AudioAttributes.Builder()
                    .setContentType(C.AUDIO_CONTENT_TYPE_MUSIC)
                    .setUsage(C.USAGE_MEDIA)
                    .build(),
                true
            )
            .setHandleAudioBecomingNoisy(true)
            .build()

        // Add player listener for debugging
        player.addListener(object : Player.Listener {
            override fun onPlaybackStateChanged(playbackState: Int) {
                val stateString = when (playbackState) {
                    Player.STATE_IDLE -> "IDLE"
                    Player.STATE_BUFFERING -> "BUFFERING"
                    Player.STATE_READY -> "READY"
                    Player.STATE_ENDED -> "ENDED"
                    else -> "UNKNOWN"
                }
                Log.d(TAG, "Playback state changed to: $stateString")
            }

            override fun onPlayerError(error: PlaybackException) {
                Log.e(TAG, "Player error: ${error.message}", error)
                Log.e(TAG, "Error code: ${error.errorCode}")
                Log.e(TAG, "Error cause: ${error.cause?.message}")
                Log.e(TAG, "Current media item: ${player.currentMediaItem?.mediaId}")
                Log.e(TAG, "Current media URI: ${player.currentMediaItem?.localConfiguration?.uri}")

                // Try to recover from certain errors
                when (error.errorCode) {
                    PlaybackException.ERROR_CODE_IO_NETWORK_CONNECTION_FAILED,
                    PlaybackException.ERROR_CODE_IO_NETWORK_CONNECTION_TIMEOUT -> {
                        Log.d(TAG, "Network error, attempting to retry...")
                        // Retry after a short delay
                        android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                            if (player.currentMediaItem != null) {
                                Log.d(TAG, "Retrying playback after network error")
                                player.prepare()
                                player.play()
                            }
                        }, 3000)
                    }
                    PlaybackException.ERROR_CODE_PARSING_CONTAINER_MALFORMED,
                    PlaybackException.ERROR_CODE_PARSING_MANIFEST_MALFORMED -> {
                        Log.e(TAG, "Media format error, skipping to next track")
                        if (player.hasNextMediaItem()) {
                            player.seekToNextMediaItem()
                        }
                    }
                    PlaybackException.ERROR_CODE_IO_INVALID_HTTP_CONTENT_TYPE,
                    PlaybackException.ERROR_CODE_IO_BAD_HTTP_STATUS -> {
                        Log.e(TAG, "HTTP error, URL might be invalid or inaccessible")
                        if (player.hasNextMediaItem()) {
                            Log.d(TAG, "Skipping to next track due to HTTP error")
                            player.seekToNextMediaItem()
                        }
                    }
                }
            }

            override fun onMediaItemTransition(mediaItem: MediaItem?, reason: Int) {
                Log.d(TAG, "Media item transition: ${mediaItem?.mediaId}, reason: $reason")
                mediaItem?.let {
                    Log.d(TAG, "New media URI: ${it.localConfiguration?.uri}")
                }
            }
        })
    }
    
    private fun initializeMediaSession() {
        val sessionActivityPendingIntent = PendingIntent.getActivity(
            this,
            0,
            Intent(this, MainActivity::class.java),
            PendingIntent.FLAG_IMMUTABLE
        )
        
        mediaSession = MediaSession.Builder(this, player)
            .setSessionActivity(sessionActivityPendingIntent)
            .setCallback(MediaSessionCallback())
            .build()
    }
    
    override fun onGetSession(controllerInfo: MediaSession.ControllerInfo): MediaSession? {
        return mediaSession
    }
    
    override fun onDestroy() {
        Log.d(TAG, "MusicService onDestroy")
        try {
            mediaSession?.run {
                if (this@MusicService::player.isInitialized) {
                    if (player.isPlaying) {
                        player.pause()
                    }
                    player.release()
                }
                release()
                mediaSession = null
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error during service cleanup", e)
        }
        super.onDestroy()
    }
    
    /**
     * Custom MediaSession callback to handle media commands
     */
    private inner class MediaSessionCallback : MediaSession.Callback {

        override fun onAddMediaItems(
            mediaSession: MediaSession,
            controller: MediaSession.ControllerInfo,
            mediaItems: MutableList<MediaItem>
        ): ListenableFuture<MutableList<MediaItem>> {
            val updatedMediaItems = mediaItems.map { mediaItem ->
                mediaItem.buildUpon()
                    .setUri(mediaItem.requestMetadata.mediaUri)
                    .build()
            }.toMutableList()
            return Futures.immediateFuture(updatedMediaItems)
        }
    }
    
    companion object {
        private const val TAG = "MusicService"

        /**
         * Convert Song object to MediaItem for ExoPlayer
         */
        fun createMediaItem(song: Song): MediaItem {
            Log.d(TAG, "Creating MediaItem for song: ${song.title}, URL: ${song.streamUrl}")

            // Validate stream URL
            if (song.streamUrl.isBlank()) {
                Log.e(TAG, "Empty stream URL for song: ${song.title}")
                throw IllegalArgumentException("Stream URL cannot be empty for song: ${song.title}")
            }

            // Parse and validate URI
            val uri = try {
                android.net.Uri.parse(song.streamUrl)
            } catch (e: Exception) {
                Log.e(TAG, "Invalid stream URL for song: ${song.title}, URL: ${song.streamUrl}", e)
                throw IllegalArgumentException("Invalid stream URL for song: ${song.title}")
            }

            if (uri == null || uri.scheme.isNullOrBlank()) {
                Log.e(TAG, "Invalid URI scheme for song: ${song.title}, URL: ${song.streamUrl}")
                throw IllegalArgumentException("Invalid URI scheme for song: ${song.title}")
            }

            Log.d(TAG, "Valid URI created: $uri")

            val metadata = MediaMetadata.Builder()
                .setTitle(song.title)
                .setArtist(song.artist)
                .setAlbumTitle(song.album)
                .setArtworkUri(android.net.Uri.parse(song.artworkUrl))
                .build()

            val mediaItem = MediaItem.Builder()
                .setUri(uri)
                .setMediaId(song.id)
                .setMediaMetadata(metadata)
                .build()

            Log.d(TAG, "MediaItem created successfully with URI: ${mediaItem.localConfiguration?.uri}")
            return mediaItem
        }
        
        /**
         * Create a list of MediaItems from a list of Songs
         */
        fun createMediaItems(songs: List<Song>): List<MediaItem> {
            val mediaItems = mutableListOf<MediaItem>()

            songs.forEach { song ->
                try {
                    val mediaItem = createMediaItem(song)
                    mediaItems.add(mediaItem)
                } catch (e: Exception) {
                    Log.e(TAG, "Failed to create MediaItem for song: ${song.title}", e)
                    // Skip invalid songs instead of failing the entire playlist
                }
            }

            if (mediaItems.isEmpty()) {
                Log.e(TAG, "No valid MediaItems created from ${songs.size} songs")
                throw IllegalArgumentException("No valid songs found in playlist")
            }

            Log.d(TAG, "Created ${mediaItems.size} MediaItems from ${songs.size} songs")
            return mediaItems
        }
    }
}
