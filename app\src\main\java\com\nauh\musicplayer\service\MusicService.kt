package com.nauh.musicplayer.service

import android.app.PendingIntent
import android.content.Intent
import android.os.Bundle
import android.util.Log
import androidx.media3.common.AudioAttributes
import androidx.media3.common.C
import androidx.media3.common.MediaItem
import androidx.media3.common.MediaMetadata
import androidx.media3.common.Player
import androidx.media3.common.PlaybackException
import androidx.media3.datasource.DefaultHttpDataSource
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.exoplayer.source.DefaultMediaSourceFactory
import androidx.media3.session.MediaSession
import androidx.media3.session.MediaSessionService
import com.google.common.util.concurrent.Futures
import com.google.common.util.concurrent.ListenableFuture
import com.nauh.musicplayer.data.model.Song
import com.nauh.musicplayer.ui.MainActivity

/**
 * Background music service using ExoPlayer and MediaSession
 * Handles audio playback, notifications, and media controls
 */
class MusicService : MediaSessionService() {

    private var mediaSession: MediaSession? = null
    private lateinit var player: ExoPlayer



    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "MusicService onCreate")
        initializePlayer()
        initializeMediaSession()
    }
    
    private fun initializePlayer() {
        Log.d(TAG, "Initializing ExoPlayer")

        // Create a custom HTTP data source factory with better error handling
        val httpDataSourceFactory = DefaultHttpDataSource.Factory()
            .setUserAgent("MusicPlayer/1.0")
            .setConnectTimeoutMs(30000)
            .setReadTimeoutMs(30000)
            .setAllowCrossProtocolRedirects(true)

        val mediaSourceFactory = DefaultMediaSourceFactory(this)
            .setDataSourceFactory(httpDataSourceFactory)

        player = ExoPlayer.Builder(this)
            .setMediaSourceFactory(mediaSourceFactory)
            .setAudioAttributes(
                AudioAttributes.Builder()
                    .setContentType(C.AUDIO_CONTENT_TYPE_MUSIC)
                    .setUsage(C.USAGE_MEDIA)
                    .build(),
                true
            )
            .setHandleAudioBecomingNoisy(true)
            .build()

        // Add player listener for debugging
        player.addListener(object : Player.Listener {
            override fun onPlaybackStateChanged(playbackState: Int) {
                val stateString = when (playbackState) {
                    Player.STATE_IDLE -> "IDLE"
                    Player.STATE_BUFFERING -> "BUFFERING"
                    Player.STATE_READY -> "READY"
                    Player.STATE_ENDED -> "ENDED"
                    else -> "UNKNOWN"
                }
                Log.d(TAG, "Playback state changed to: $stateString")
            }

            override fun onPlayerError(error: PlaybackException) {
                Log.e(TAG, "Player error: ${error.message}", error)
                Log.e(TAG, "Error code: ${error.errorCode}")
                Log.e(TAG, "Error cause: ${error.cause?.message}")
                Log.e(TAG, "Current media item: ${player.currentMediaItem?.mediaId}")
                Log.e(TAG, "Current media URI: ${player.currentMediaItem?.localConfiguration?.uri}")

                // Try to recover from certain errors
                when (error.errorCode) {
                    PlaybackException.ERROR_CODE_IO_NETWORK_CONNECTION_FAILED,
                    PlaybackException.ERROR_CODE_IO_NETWORK_CONNECTION_TIMEOUT -> {
                        Log.d(TAG, "Network error, attempting to retry...")
                        // Could implement retry logic here
                    }
                    PlaybackException.ERROR_CODE_PARSING_CONTAINER_MALFORMED,
                    PlaybackException.ERROR_CODE_PARSING_MANIFEST_MALFORMED -> {
                        Log.e(TAG, "Media format error, skipping to next track")
                        // Could skip to next track automatically
                    }
                    PlaybackException.ERROR_CODE_IO_INVALID_HTTP_CONTENT_TYPE,
                    PlaybackException.ERROR_CODE_IO_BAD_HTTP_STATUS -> {
                        Log.e(TAG, "HTTP error, URL might be invalid or inaccessible")
                    }
                }
            }

            override fun onMediaItemTransition(mediaItem: MediaItem?, reason: Int) {
                Log.d(TAG, "Media item transition: ${mediaItem?.mediaId}, reason: $reason")
                mediaItem?.let {
                    Log.d(TAG, "New media URI: ${it.localConfiguration?.uri}")
                }
            }
        })
    }
    
    private fun initializeMediaSession() {
        val sessionActivityPendingIntent = PendingIntent.getActivity(
            this,
            0,
            Intent(this, MainActivity::class.java),
            PendingIntent.FLAG_IMMUTABLE
        )
        
        mediaSession = MediaSession.Builder(this, player)
            .setSessionActivity(sessionActivityPendingIntent)
            .setCallback(MediaSessionCallback())
            .build()
    }
    
    override fun onGetSession(controllerInfo: MediaSession.ControllerInfo): MediaSession? {
        return mediaSession
    }
    
    override fun onDestroy() {
        mediaSession?.run {
            player.release()
            release()
            mediaSession = null
        }
        super.onDestroy()
    }
    
    /**
     * Custom MediaSession callback to handle media commands
     */
    private inner class MediaSessionCallback : MediaSession.Callback {

        override fun onAddMediaItems(
            mediaSession: MediaSession,
            controller: MediaSession.ControllerInfo,
            mediaItems: MutableList<MediaItem>
        ): ListenableFuture<MutableList<MediaItem>> {
            val updatedMediaItems = mediaItems.map { mediaItem ->
                mediaItem.buildUpon()
                    .setUri(mediaItem.requestMetadata.mediaUri)
                    .build()
            }.toMutableList()
            return Futures.immediateFuture(updatedMediaItems)
        }
    }
    
    companion object {
        private const val TAG = "MusicService"

        /**
         * Convert Song object to MediaItem for ExoPlayer
         */
        fun createMediaItem(song: Song): MediaItem {
            Log.d(TAG, "Creating MediaItem for song: ${song.title}, URL: ${song.streamUrl}")

            val metadata = MediaMetadata.Builder()
                .setTitle(song.title)
                .setArtist(song.artist)
                .setAlbumTitle(song.album)
                .setArtworkUri(android.net.Uri.parse(song.artworkUrl))
                .build()

            val mediaItem = MediaItem.Builder()
                .setUri(song.streamUrl)
                .setMediaId(song.id)
                .setMediaMetadata(metadata)
                .build()

            Log.d(TAG, "MediaItem created with URI: ${mediaItem.localConfiguration?.uri}")
            return mediaItem
        }
        
        /**
         * Create a list of MediaItems from a list of Songs
         */
        fun createMediaItems(songs: List<Song>): List<MediaItem> {
            return songs.map { createMediaItem(it) }
        }
    }
}
