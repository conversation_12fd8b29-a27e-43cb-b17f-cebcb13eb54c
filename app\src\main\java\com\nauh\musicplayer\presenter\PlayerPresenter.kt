package com.nauh.musicplayer.presenter

import android.content.Context
import android.util.Log
import com.nauh.musicplayer.contract.PlayerContract
import com.nauh.musicplayer.data.model.Song
import com.nauh.musicplayer.service.MusicServiceConnection

/**
 * Presenter for the Player screen
 * Handles business logic for music playback and communication between View and Service
 */
class PlayerPresenter(private val context: Context) : PlayerContract.Presenter {

    private var view: PlayerContract.View? = null
    private var currentSong: Song? = null
    private var playlist: List<Song> = emptyList()
    private var currentIndex: Int = 0
    private var isShuffled: Boolean = false
    private var repeatMode: Int = PlayerContract.RepeatMode.OFF
    private var musicServiceConnection: MusicServiceConnection? = null

    companion object {
        private const val TAG = "PlayerPresenter"
    }
    
    override fun attachView(view: PlayerContract.View) {
        this.view = view
        initializeMusicService()
    }

    override fun detachView() {
        this.view = null
        musicServiceConnection?.disconnect()
    }

    private fun initializeMusicService() {
        musicServiceConnection = MusicServiceConnection(context)
        musicServiceConnection?.setPlaybackStateListener(object : MusicServiceConnection.PlaybackStateListener {
            override fun onPlaybackStateChanged(isPlaying: Boolean) {
                view?.updatePlayPauseButton(isPlaying)
            }

            override fun onProgressUpdate(position: Long, duration: Long) {
                view?.updateProgress(position, duration)

                // Update seek bar
                val progress = if (duration > 0) {
                    ((position.toFloat() / duration.toFloat()) * 100).toInt()
                } else 0
                view?.updateSeekBar(progress, 100)
            }

            override fun onSongChanged(song: Song?) {
                song?.let {
                    currentSong = it
                    view?.showSongInfo(it)
                }
            }

            override fun onPlaybackError(error: String) {
                Log.e(TAG, "Playback error: $error")
                view?.showError("Playback Error: $error")
            }

            override fun onConnectionError(error: String) {
                Log.e(TAG, "Connection error: $error")
                view?.showError("Connection Error: $error")
            }
        })
        musicServiceConnection?.connect()
    }
    
    override fun initializePlayer(song: Song, playlist: List<Song>) {
        Log.d(TAG, "initializePlayer called with song: ${song.title}, playlist size: ${playlist.size}")
        this.currentSong = song
        this.playlist = playlist
        this.currentIndex = playlist.indexOf(song).takeIf { it >= 0 } ?: 0

        Log.d(TAG, "Current index: $currentIndex")

        view?.showSongInfo(song)
        view?.updatePlaylist(playlist, currentIndex)
        updateNavigationButtons()

        // Start playing the song
        Log.d(TAG, "Starting playback...")
        musicServiceConnection?.playPlaylist(playlist, currentIndex)
    }

    override fun playPause() {
        musicServiceConnection?.playPause()
    }

    override fun seekTo(position: Long) {
        musicServiceConnection?.seekTo(position)
    }
    
    override fun skipToNext() {
        if (currentIndex < playlist.size - 1) {
            currentIndex++
            currentSong = playlist[currentIndex]
            view?.showSongInfo(currentSong!!)
        }
        musicServiceConnection?.skipToNext()
        updateNavigationButtons()
    }

    override fun skipToPrevious() {
        if (currentIndex > 0) {
            currentIndex--
            currentSong = playlist[currentIndex]
            view?.showSongInfo(currentSong!!)
        }
        musicServiceConnection?.skipToPrevious()
        updateNavigationButtons()
    }
    
    override fun toggleShuffle() {
        isShuffled = !isShuffled
        view?.showShuffleState(isShuffled)
        updateNavigationButtons()
    }
    
    override fun toggleRepeat() {
        repeatMode = when (repeatMode) {
            PlayerContract.RepeatMode.OFF -> PlayerContract.RepeatMode.ALL
            PlayerContract.RepeatMode.ALL -> PlayerContract.RepeatMode.ONE
            PlayerContract.RepeatMode.ONE -> PlayerContract.RepeatMode.OFF
            else -> PlayerContract.RepeatMode.OFF
        }
        view?.showRepeatState(repeatMode)
        updateNavigationButtons()
    }
    
    override fun onProgressUpdate(position: Long, duration: Long) {
        view?.updateProgress(position, duration)
        
        // Update seek bar
        val progress = if (duration > 0) {
            ((position.toFloat() / duration.toFloat()) * 100).toInt()
        } else 0
        view?.updateSeekBar(progress, 100)
    }
    
    override fun onPlaybackStateChanged(isPlaying: Boolean) {
        view?.updatePlayPauseButton(isPlaying)
    }
    
    override fun onSongChanged(song: Song) {
        currentSong = song
        view?.showSongInfo(song)
    }
    
    override fun onPlaylistChanged(songs: List<Song>, currentIndex: Int) {
        this.playlist = songs
        this.currentIndex = currentIndex
        view?.updatePlaylist(songs, currentIndex)
        updateNavigationButtons()
    }
    
    private fun updateNavigationButtons() {
        val canGoPrevious = when {
            isShuffled -> playlist.size > 1
            repeatMode == PlayerContract.RepeatMode.ALL -> playlist.size > 1
            else -> currentIndex > 0
        }
        
        val canGoNext = when {
            isShuffled -> playlist.size > 1
            repeatMode == PlayerContract.RepeatMode.ALL -> playlist.size > 1
            else -> currentIndex < playlist.size - 1
        }
        
        view?.enablePreviousButton(canGoPrevious)
        view?.enableNextButton(canGoNext)
    }
}
