<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="16dp"
    android:background="?android:attr/selectableItemBackground"
    android:clickable="true"
    android:focusable="true"
    android:gravity="center_vertical">

    <!-- Album Artwork (Circular) -->
    <de.hdodenhof.circleimageview.CircleImageView
        android:id="@+id/albumArtwork"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:src="@drawable/placeholder_album_art"
        app:civ_border_width="2dp"
        app:civ_border_color="@color/primary" />

    <!-- Song Info Container -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="8dp"
        android:orientation="vertical">

        <!-- Song Title -->
        <TextView
            android:id="@+id/songTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold"
            tools:text="12600 lettres (Débat)" />

        <!-- Artist Name -->
        <TextView
            android:id="@+id/artistAlbum"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/text_secondary"
            android:textSize="14sp"
            tools:text="Franco &amp; TP OK Jazz" />

    </LinearLayout>

    <!-- Song Duration -->
    <TextView
        android:id="@+id/songDuration"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="8dp"
        android:textColor="@color/text_secondary"
        android:textSize="12sp"
        tools:text="3:45" />

    <!-- More Options Button -->
    <ImageButton
        android:id="@+id/moreOptions"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:background="?android:attr/selectableItemBackgroundBorderless"
        android:src="@drawable/ic_more_vert"
        android:contentDescription="@string/more_options"
        android:tint="@color/text_secondary" />

    <!-- Playing Indicator (hidden by default) -->
    <ImageView
        android:id="@+id/playingIndicator"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_marginEnd="8dp"
        android:src="@drawable/ic_equalizer"
        android:visibility="gone"
        android:tint="@color/primary" />

</LinearLayout>
