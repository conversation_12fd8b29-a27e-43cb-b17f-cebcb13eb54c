package com.nauh.musicplayer.service

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.IBinder
import android.util.Log
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import androidx.media3.session.MediaController
import androidx.media3.session.SessionToken
import com.google.common.util.concurrent.ListenableFuture
import com.google.common.util.concurrent.MoreExecutors
import com.nauh.musicplayer.data.model.Song

/**
 * Service connection class to manage communication between UI and MusicService
 * Handles MediaController setup and provides playback control methods
 */
class MusicServiceConnection(private val context: Context) {

    private var mediaControllerFuture: ListenableFuture<MediaController>? = null
    private var mediaController: MediaController? = null
    private var playbackStateListener: PlaybackStateListener? = null
    private var isConnected = false
    private var pendingPlaylist: Pair<List<Song>, Int>? = null
    private var connectionRetryCount = 0
    private val maxRetryAttempts = 3
    private var progressUpdateHandler: android.os.Handler? = null
    private var progressUpdateRunnable: Runnable? = null

    companion object {
        private const val TAG = "MusicServiceConnection"
        private const val CONNECTION_TIMEOUT_MS = 10000L // 10 seconds
    }
    
    interface PlaybackStateListener {
        fun onPlaybackStateChanged(isPlaying: Boolean)
        fun onProgressUpdate(position: Long, duration: Long)
        fun onSongChanged(song: Song?)
        fun onPlaybackError(error: String)
        fun onConnectionError(error: String)
    }
    
    fun setPlaybackStateListener(listener: PlaybackStateListener) {
        this.playbackStateListener = listener
    }
    
    fun connect() {
        Log.d(TAG, "Connecting to MusicService... (attempt ${connectionRetryCount + 1})")

        // Start the service first to ensure it's running
        val serviceIntent = Intent(context, MusicService::class.java)
        context.startService(serviceIntent)

        val sessionToken = SessionToken(context, ComponentName(context, MusicService::class.java))
        mediaControllerFuture = MediaController.Builder(context, sessionToken).buildAsync()

        // Add timeout handling
        val timeoutHandler = android.os.Handler(android.os.Looper.getMainLooper())
        val timeoutRunnable = Runnable {
            if (!isConnected) {
                Log.e(TAG, "MediaController connection timeout after ${CONNECTION_TIMEOUT_MS}ms")
                handleConnectionFailure()
            }
        }
        timeoutHandler.postDelayed(timeoutRunnable, CONNECTION_TIMEOUT_MS)

        mediaControllerFuture?.addListener({
            timeoutHandler.removeCallbacks(timeoutRunnable) // Cancel timeout
            try {
                mediaController = mediaControllerFuture?.get()
                isConnected = true
                connectionRetryCount = 0 // Reset retry count on successful connection
                Log.d(TAG, "MediaController connected successfully")
                Log.d(TAG, "MediaController state: ${mediaController?.playbackState}")
                Log.d(TAG, "MediaController isPlaying: ${mediaController?.isPlaying}")

                mediaController?.addListener(object : Player.Listener {
                    override fun onPlaybackStateChanged(playbackState: Int) {
                        val stateString = when (playbackState) {
                            Player.STATE_IDLE -> "IDLE"
                            Player.STATE_BUFFERING -> "BUFFERING"
                            Player.STATE_READY -> "READY"
                            Player.STATE_ENDED -> "ENDED"
                            else -> "UNKNOWN"
                        }
                        Log.d(TAG, "Player state changed to: $stateString")
                        val isPlaying = mediaController?.isPlaying ?: false
                        playbackStateListener?.onPlaybackStateChanged(isPlaying)
                    }

                    override fun onIsPlayingChanged(isPlaying: Boolean) {
                        Log.d(TAG, "Is playing changed to: $isPlaying")
                        playbackStateListener?.onPlaybackStateChanged(isPlaying)
                    }



                    override fun onPositionDiscontinuity(
                        oldPosition: Player.PositionInfo,
                        newPosition: Player.PositionInfo,
                        reason: Int
                    ) {
                        updateProgress()
                    }

                    override fun onMediaItemTransition(mediaItem: MediaItem?, reason: Int) {
                        Log.d(TAG, "Media item transition: ${mediaItem?.mediaId}")
                        // Handle song change - convert MediaItem back to Song
                        mediaItem?.let { item ->
                            val song = Song(
                                id = item.mediaId ?: "",
                                title = item.mediaMetadata.title?.toString() ?: "Unknown",
                                artist = item.mediaMetadata.artist?.toString() ?: "Unknown",
                                album = item.mediaMetadata.albumTitle?.toString() ?: "Unknown",
                                duration = mediaController?.duration ?: 0L,
                                artworkUrl = item.mediaMetadata.artworkUri?.toString() ?: "",
                                streamUrl = item.localConfiguration?.uri?.toString() ?: ""
                            )
                            playbackStateListener?.onSongChanged(song)
                        }
                    }
                })

                // Start progress updates
                startProgressUpdates()

                // Play pending playlist if any
                pendingPlaylist?.let { (songs, startIndex) ->
                    Log.d(TAG, "Playing pending playlist with ${songs.size} songs")
                    playPlaylistInternal(songs, startIndex)
                    pendingPlaylist = null
                }

            } catch (e: Exception) {
                Log.e(TAG, "Error connecting MediaController", e)
                handleConnectionFailure()
            }
        }, MoreExecutors.directExecutor())
    }

    private fun handleConnectionFailure() {
        connectionRetryCount++
        if (connectionRetryCount < maxRetryAttempts) {
            Log.d(TAG, "Retrying connection in 2 seconds...")
            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                connect()
            }, 2000)
        } else {
            Log.e(TAG, "Failed to connect after $maxRetryAttempts attempts")
            playbackStateListener?.onConnectionError("Failed to connect to music service after $maxRetryAttempts attempts")
        }
    }
    
    fun disconnect() {
        Log.d(TAG, "Disconnecting from MusicService")
        isConnected = false
        pendingPlaylist = null

        // Stop progress updates
        progressUpdateRunnable?.let { runnable ->
            progressUpdateHandler?.removeCallbacks(runnable)
        }
        progressUpdateHandler = null
        progressUpdateRunnable = null

        mediaController?.release()
        mediaControllerFuture?.cancel(true)
        mediaController = null
        mediaControllerFuture = null
    }
    
    fun playSong(song: Song) {
        val mediaItem = MusicService.createMediaItem(song)
        mediaController?.setMediaItem(mediaItem)
        mediaController?.prepare()
        mediaController?.play()
    }
    
    fun playPlaylist(songs: List<Song>, startIndex: Int = 0) {
        Log.d(TAG, "playPlaylist called with ${songs.size} songs, startIndex: $startIndex")

        if (songs.isEmpty()) {
            Log.e(TAG, "Cannot play empty playlist")
            return
        }

        if (startIndex < 0 || startIndex >= songs.size) {
            Log.e(TAG, "Invalid startIndex: $startIndex for playlist size: ${songs.size}")
            return
        }

        if (isConnected && mediaController != null) {
            Log.d(TAG, "MediaController ready, playing immediately")
            playPlaylistInternal(songs, startIndex)
        } else {
            Log.d(TAG, "MediaController not ready, storing playlist for later")
            pendingPlaylist = Pair(songs, startIndex)

            // If not connected, try to connect now
            if (!isConnected) {
                Log.d(TAG, "Attempting to connect to service...")
                connect()
            }
        }
    }

    private fun playPlaylistInternal(songs: List<Song>, startIndex: Int = 0) {
        Log.d(TAG, "playPlaylistInternal called with ${songs.size} songs, startIndex: $startIndex")

        if (songs.isEmpty()) {
            Log.e(TAG, "Cannot play empty playlist")
            return
        }

        if (startIndex < 0 || startIndex >= songs.size) {
            Log.e(TAG, "Invalid startIndex: $startIndex for playlist size: ${songs.size}")
            return
        }

        val mediaItems = try {
            MusicService.createMediaItems(songs)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to create MediaItems from songs", e)
            val errorMessage = when {
                e.message?.contains("No valid songs") == true -> "No playable songs found in the playlist"
                e.message?.contains("Invalid") == true -> "Some songs have invalid URLs and cannot be played"
                else -> "Failed to prepare songs for playback: ${e.message}"
            }
            playbackStateListener?.onPlaybackError(errorMessage)
            return
        }

        if (mediaItems.isEmpty()) {
            Log.e(TAG, "No valid MediaItems created")
            playbackStateListener?.onPlaybackError("No playable songs found")
            return
        }

        // Adjust startIndex if some songs were skipped due to invalid URLs
        val adjustedStartIndex = if (startIndex >= mediaItems.size) {
            Log.w(TAG, "Adjusted startIndex from $startIndex to 0 due to invalid songs")
            0
        } else {
            startIndex
        }

        // Log each media item for debugging
        mediaItems.forEachIndexed { index, mediaItem ->
            Log.d(TAG, "MediaItem $index: ${mediaItem.mediaId} - ${mediaItem.localConfiguration?.uri}")
        }

        mediaController?.let { controller ->
            Log.d(TAG, "Setting ${mediaItems.size} media items and starting playback at index $adjustedStartIndex")
            Log.d(TAG, "Controller current state: ${controller.playbackState}")

            try {
                controller.setMediaItems(mediaItems, adjustedStartIndex, 0)
                controller.prepare()
                controller.play()

                Log.d(TAG, "Playback commands sent successfully")
                Log.d(TAG, "Controller state after play: ${controller.playbackState}")
                Log.d(TAG, "Controller isPlaying: ${controller.isPlaying}")
            } catch (e: Exception) {
                Log.e(TAG, "Error setting media items or starting playback", e)
                playbackStateListener?.onPlaybackStateChanged(false)
            }
        } ?: Log.e(TAG, "MediaController is null, cannot play playlist")
    }
    
    fun playPause() {
        mediaController?.let { controller ->
            if (controller.isPlaying) {
                controller.pause()
            } else {
                controller.play()
            }
        }
    }
    
    fun skipToNext() {
        Log.d(TAG, "skipToNext called")
        mediaController?.let { controller ->
            if (controller.hasNextMediaItem()) {
                controller.seekToNextMediaItem()
                Log.d(TAG, "Skipped to next track")
            } else {
                Log.d(TAG, "No next track available")
            }
        } ?: Log.e(TAG, "MediaController is null, cannot skip to next")
    }

    fun skipToPrevious() {
        Log.d(TAG, "skipToPrevious called")
        mediaController?.let { controller ->
            if (controller.hasPreviousMediaItem()) {
                controller.seekToPreviousMediaItem()
                Log.d(TAG, "Skipped to previous track")
            } else {
                Log.d(TAG, "No previous track available")
            }
        } ?: Log.e(TAG, "MediaController is null, cannot skip to previous")
    }
    
    fun seekTo(position: Long) {
        mediaController?.seekTo(position)
    }
    
    fun isPlaying(): Boolean {
        return mediaController?.isPlaying ?: false
    }
    
    fun getCurrentPosition(): Long {
        return mediaController?.currentPosition ?: 0L
    }
    
    fun getDuration(): Long {
        return mediaController?.duration ?: 0L
    }
    
    private fun startProgressUpdates() {
        // Stop any existing progress updates
        progressUpdateRunnable?.let { runnable ->
            progressUpdateHandler?.removeCallbacks(runnable)
        }

        // Start a handler to update progress regularly
        progressUpdateHandler = android.os.Handler(android.os.Looper.getMainLooper())
        progressUpdateRunnable = object : Runnable {
            override fun run() {
                if (isConnected && mediaController != null) {
                    updateProgress()
                    progressUpdateHandler?.postDelayed(this, 1000) // Update every second
                }
            }
        }
        progressUpdateHandler?.post(progressUpdateRunnable!!)
    }
    
    private fun updateProgress() {
        val position = getCurrentPosition()
        val duration = getDuration()
        playbackStateListener?.onProgressUpdate(position, duration)
    }
}
