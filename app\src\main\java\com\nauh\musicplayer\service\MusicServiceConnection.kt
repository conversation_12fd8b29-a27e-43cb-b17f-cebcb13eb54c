package com.nauh.musicplayer.service

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.IBinder
import android.util.Log
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import androidx.media3.session.MediaController
import androidx.media3.session.SessionToken
import com.google.common.util.concurrent.ListenableFuture
import com.google.common.util.concurrent.MoreExecutors
import com.nauh.musicplayer.data.model.Song

/**
 * Service connection class to manage communication between UI and MusicService
 * Handles MediaController setup and provides playback control methods
 */
class MusicServiceConnection(private val context: Context) {

    private var mediaControllerFuture: ListenableFuture<MediaController>? = null
    private var mediaController: MediaController? = null
    private var playbackStateListener: PlaybackStateListener? = null
    private var isConnected = false
    private var pendingPlaylist: Pair<List<Song>, Int>? = null

    companion object {
        private const val TAG = "MusicServiceConnection"
    }
    
    interface PlaybackStateListener {
        fun onPlaybackStateChanged(isPlaying: Boolean)
        fun onProgressUpdate(position: Long, duration: Long)
        fun onSongChanged(song: Song?)
    }
    
    fun setPlaybackStateListener(listener: PlaybackStateListener) {
        this.playbackStateListener = listener
    }
    
    fun connect() {
        Log.d(TAG, "Connecting to MusicService...")
        // Start the service first to ensure it's running
        val serviceIntent = Intent(context, MusicService::class.java)
        context.startService(serviceIntent)

        val sessionToken = SessionToken(context, ComponentName(context, MusicService::class.java))
        mediaControllerFuture = MediaController.Builder(context, sessionToken).buildAsync()

        mediaControllerFuture?.addListener({
            try {
                mediaController = mediaControllerFuture?.get()
                isConnected = true
                Log.d(TAG, "MediaController connected successfully")
                Log.d(TAG, "MediaController state: ${mediaController?.playbackState}")
                Log.d(TAG, "MediaController isPlaying: ${mediaController?.isPlaying}")

                mediaController?.addListener(object : Player.Listener {
                    override fun onPlaybackStateChanged(playbackState: Int) {
                        val stateString = when (playbackState) {
                            Player.STATE_IDLE -> "IDLE"
                            Player.STATE_BUFFERING -> "BUFFERING"
                            Player.STATE_READY -> "READY"
                            Player.STATE_ENDED -> "ENDED"
                            else -> "UNKNOWN"
                        }
                        Log.d(TAG, "Player state changed to: $stateString")
                        val isPlaying = mediaController?.isPlaying ?: false
                        playbackStateListener?.onPlaybackStateChanged(isPlaying)
                    }

                    override fun onIsPlayingChanged(isPlaying: Boolean) {
                        Log.d(TAG, "Is playing changed to: $isPlaying")
                        playbackStateListener?.onPlaybackStateChanged(isPlaying)
                    }

                    override fun onPositionDiscontinuity(
                        oldPosition: Player.PositionInfo,
                        newPosition: Player.PositionInfo,
                        reason: Int
                    ) {
                        updateProgress()
                    }

                    override fun onMediaItemTransition(mediaItem: MediaItem?, reason: Int) {
                        Log.d(TAG, "Media item transition: ${mediaItem?.mediaId}")
                        // Handle song change
                        mediaItem?.let {
                            // Convert MediaItem back to Song if needed
                            playbackStateListener?.onSongChanged(null) // TODO: Convert MediaItem to Song
                        }
                    }
                })

                // Start progress updates
                startProgressUpdates()

                // Play pending playlist if any
                pendingPlaylist?.let { (songs, startIndex) ->
                    Log.d(TAG, "Playing pending playlist")
                    playPlaylistInternal(songs, startIndex)
                    pendingPlaylist = null
                }

            } catch (e: Exception) {
                Log.e(TAG, "Error connecting MediaController", e)
                e.printStackTrace()
            }
        }, MoreExecutors.directExecutor())
    }
    
    fun disconnect() {
        Log.d(TAG, "Disconnecting from MusicService")
        isConnected = false
        pendingPlaylist = null
        mediaController?.release()
        mediaControllerFuture?.cancel(true)
        mediaController = null
        mediaControllerFuture = null
    }
    
    fun playSong(song: Song) {
        val mediaItem = MusicService.createMediaItem(song)
        mediaController?.setMediaItem(mediaItem)
        mediaController?.prepare()
        mediaController?.play()
    }
    
    fun playPlaylist(songs: List<Song>, startIndex: Int = 0) {
        Log.d(TAG, "playPlaylist called with ${songs.size} songs, startIndex: $startIndex")
        if (isConnected && mediaController != null) {
            playPlaylistInternal(songs, startIndex)
        } else {
            Log.d(TAG, "MediaController not ready, storing playlist for later")
            pendingPlaylist = Pair(songs, startIndex)
        }
    }

    private fun playPlaylistInternal(songs: List<Song>, startIndex: Int = 0) {
        Log.d(TAG, "playPlaylistInternal called with ${songs.size} songs, startIndex: $startIndex")

        if (songs.isEmpty()) {
            Log.e(TAG, "Cannot play empty playlist")
            return
        }

        if (startIndex < 0 || startIndex >= songs.size) {
            Log.e(TAG, "Invalid startIndex: $startIndex for playlist size: ${songs.size}")
            return
        }

        val mediaItems = MusicService.createMediaItems(songs)

        // Log each media item for debugging
        mediaItems.forEachIndexed { index, mediaItem ->
            Log.d(TAG, "MediaItem $index: ${mediaItem.mediaId} - ${mediaItem.localConfiguration?.uri}")
        }

        mediaController?.let { controller ->
            Log.d(TAG, "Setting media items and starting playback")
            Log.d(TAG, "Controller current state: ${controller.playbackState}")

            controller.setMediaItems(mediaItems, startIndex, 0)
            controller.prepare()
            controller.play()

            Log.d(TAG, "Playback commands sent")
            Log.d(TAG, "Controller state after play: ${controller.playbackState}")
            Log.d(TAG, "Controller isPlaying: ${controller.isPlaying}")
        } ?: Log.e(TAG, "MediaController is null, cannot play playlist")
    }
    
    fun playPause() {
        mediaController?.let { controller ->
            if (controller.isPlaying) {
                controller.pause()
            } else {
                controller.play()
            }
        }
    }
    
    fun skipToNext() {
        Log.d(TAG, "skipToNext called")
        mediaController?.let { controller ->
            if (controller.hasNextMediaItem()) {
                controller.seekToNextMediaItem()
                Log.d(TAG, "Skipped to next track")
            } else {
                Log.d(TAG, "No next track available")
            }
        } ?: Log.e(TAG, "MediaController is null, cannot skip to next")
    }

    fun skipToPrevious() {
        Log.d(TAG, "skipToPrevious called")
        mediaController?.let { controller ->
            if (controller.hasPreviousMediaItem()) {
                controller.seekToPreviousMediaItem()
                Log.d(TAG, "Skipped to previous track")
            } else {
                Log.d(TAG, "No previous track available")
            }
        } ?: Log.e(TAG, "MediaController is null, cannot skip to previous")
    }
    
    fun seekTo(position: Long) {
        mediaController?.seekTo(position)
    }
    
    fun isPlaying(): Boolean {
        return mediaController?.isPlaying ?: false
    }
    
    fun getCurrentPosition(): Long {
        return mediaController?.currentPosition ?: 0L
    }
    
    fun getDuration(): Long {
        return mediaController?.duration ?: 0L
    }
    
    private fun startProgressUpdates() {
        // Start a handler to update progress regularly
        val handler = android.os.Handler(android.os.Looper.getMainLooper())
        val progressRunnable = object : Runnable {
            override fun run() {
                if (mediaController != null) {
                    updateProgress()
                    handler.postDelayed(this, 1000) // Update every second
                }
            }
        }
        handler.post(progressRunnable)
    }
    
    private fun updateProgress() {
        val position = getCurrentPosition()
        val duration = getDuration()
        playbackStateListener?.onProgressUpdate(position, duration)
    }
}
