package com.nauh.musicplayer.data.api

import com.nauh.musicplayer.data.model.Song
import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Path

/**
 * API interface for fetching music data from remote sources
 */
interface MusicApiService {
    
    /**
     * Fetch all available songs
     */
    @GET("songs")
    suspend fun getAllSongs(): Response<List<Song>>
    
    /**
     * Fetch songs by genre
     */
    @GET("songs/genre/{genre}")
    suspend fun getSongsByGenre(@Path("genre") genre: String): Response<List<Song>>
    
    /**
     * Fetch song by ID
     */
    @GET("songs/{id}")
    suspend fun getSongById(@Path("id") id: String): Response<Song>
    
    /**
     * Search songs by query
     */
    @GET("songs/search/{query}")
    suspend fun searchSongs(@Path("query") query: String): Response<List<Song>>
}

/**
 * Mock data provider for demonstration purposes
 * In a real app, this would be replaced with actual API calls
 */
object MockMusicData {
    
    fun getSampleSongs(): List<Song> {
        return listOf(
            Song(
                id = "1",
                title = "Sample Audio Track 1",
                artist = "Test Artist",
                album = "Demo Album",
                duration = 180000, // 3 minutes
                artworkUrl = "https://via.placeholder.com/300x300/E91E63/FFFFFF?text=🎵",
                streamUrl = "https://commondatastorage.googleapis.com/codeskulptor-demos/DDR_assets/Kangaroo_MusiQue_-_The_Neverwritten_Role_Playing_Game.mp3",
                genre = "Demo"
            ),
            Song(
                id = "2",
                title = "Sample Audio Track 2",
                artist = "Test Artist",
                album = "Demo Album",
                duration = 210000, // 3.5 minutes
                artworkUrl = "https://via.placeholder.com/300x300/2196F3/FFFFFF?text=🎵",
                streamUrl = "https://www.learningcontainer.com/wp-content/uploads/2020/02/Kalimba.mp3",
                genre = "Demo"
            ),
            Song(
                id = "3",
                title = "Sample Audio Track 3",
                artist = "Test Artist",
                album = "Demo Album",
                duration = 195000, // 3.25 minutes
                artworkUrl = "https://via.placeholder.com/300x300/FF9800/FFFFFF?text=🎵",
                streamUrl = "https://file-examples.com/storage/fe68c8777b8ee92c7178b6d/2017/11/file_example_MP3_700KB.mp3",
                genre = "Demo"
            ),
            Song(
                id = "4",
                title = "Sample Audio Track 4",
                artist = "Test Artist",
                album = "Demo Album",
                duration = 165000, // 2.75 minutes
                artworkUrl = "https://via.placeholder.com/300x300/4CAF50/FFFFFF?text=🎵",
                streamUrl = "https://commondatastorage.googleapis.com/codeskulptor-assets/Epoq-Lepidoptera.ogg",
                genre = "Demo"
            ),
            Song(
                id = "5",
                title = "Sample Audio Track 5",
                artist = "Test Artist",
                album = "Demo Album",
                duration = 225000, // 3.75 minutes
                artworkUrl = "https://via.placeholder.com/300x300/FF5722/FFFFFF?text=🎵",
                streamUrl = "https://www.soundjay.com/misc/sounds/bell-ringing-05.mp3",
                genre = "Demo"
            )
        )
    }
}
